-- local scrollBackgroundWidth = rect.right / 1.71
-- local scrollBackgroundHeight = rect.bottom / 1.325

-- -- Calculate some window sizes and window positions
-- local backgroundWindowPosRight = rect.right / 4.65
-- local backgroundWindowPosBottom = rect.bottom / (rect.bottom / 20) - 20
-- local backgroundWindowSizeRight = rect.right / 1.7

-- -- Drawing a centered scroll with a background image
-- GUI_OPEN = false
-- local backgroundWindowSizeBottom = rect.bottom / 1

-- -- function test()
-- -- 	-- TEST FOR BACKROUNG WINDOW
-- 	-- Set the next window position and size
-- 	ImGui.SetNextWindowPos(backgroundWindowPosRight, backgroundWindowPosBottom, ImGuiCond.Appearing)
-- 	ImGui.SetNextWindowSize(backgroundWindowSizeRight, backgroundWindowSizeBottom)

-- 	-- Start drawing the centered window (anything before ImGui.End() below will be drawn in this window )
-- 	local flags = ImGuiWindowFlags.NoDecoration + ImGuiWindowFlags.NoBackground
-- 	GUI_OPEN, shouldDraw = ImGui.Begin("BACKGROUND_SCROLL", GUI_OPEN, flags)

-- 	-- Calculate some image sizes

-- 	-- Draw the image in our centered window
-- 	-- ImGui.Image(image, scrollBackgroundWidth, scrollBackgroundHeight)
-- 	ImGui.Image(BANNER_IMAGE.img, scrollBackgroundWidth, scrollBackgroundHeight)

-- 	ImGui.End() -- End window area
-- end


---@IDEA for later : a way to mount/dismount cavalry units before engaging battle 
---@IDEA : make the groundwork for an event system :
---         - for neighbours
---         - for allies/enemies
---         - for trade partners
---@IDEA : manage the rebel armies by script instead of by the base game (allow subfactions, different kind of revolts, giving objectives to rebelarmies, etc...)





local function test()
	-- battleAI.setGtaPlan(M2TW.battle.sides[2].battleAIPlan, 'attackAll')
	-- battleAI.setGtaObjective(M2TW.battle.sides[2], 'SALLY_OUT', 999)
	-- M2TWEOP.scriptCommand('ai_gta_add_objective', '1 MOVE_TO_POINT 11 440, 710')
	-- populationCap.checkPopCap(M2TW.campaign.currentFaction)
	-- local dbs = M2TWEOP:getCampaignDb()
	-- dbs.recruitment.maxAgentsPerTurn = 1
	-- print('recruitment db :'..
	-- 	'\n\t- recruitmentSlots : '..dbs.recruitment.recruitmentSlots..
	-- 	'\n\t- retrainingSlots : '..dbs.recruitment.retrainingSlots..
	-- 	'\n\t- deplenishPoolsWithCaps : '..tostring(dbs.recruitment.deplenishPoolsWithCaps)..
	-- 	'\n\t- deplenishMultiplier : '..dbs.recruitment.deplenishMultiplier..
	-- 	'\n\t- deplenishOffset : '..dbs.recruitment.deplenishOffset..
	-- 	'\n\t- addDisbandNoCaps : '..tostring(dbs.recruitment.addDisbandNoCaps)..
	-- 	'\n\t- percentagePoolReductionLost : '..dbs.recruitment.percentagePoolReductionLost..
	-- 	'\n\t- percentagePoolReductionOccupy : '..dbs.recruitment.percentagePoolReductionOccupy..
	-- 	'\n\t- percentagePoolReductionSack : '..dbs.recruitment.percentagePoolReductionSack..
	-- 	'\n\t- percentagePoolReductionExterminate : '..dbs.recruitment.percentagePoolReductionExterminate..
	-- 	'\n\t- maxAgentsPerTurn : '..dbs.recruitment.maxAgentsPerTurn
	-- )
end

-- ---Exports: faction, targetFaction, resourceDescription, religion, targetReligion
-- ---Valid transgression strings : : TC_THREATEN_WAR, TC_DECLARED_WAR, TC_MINOR_ASSASSINATION_ATTEMPT, 
-- ---TC_BROKE_TREATY_TERMS, TC_BROKE_ALLIANCE, TC_INVASION
-- ---@TODO test for TC_DECLARED_WAR : when does it happen?
-- ---@TODO report: there is other kind of transgression, can they be used in checks? TC_BATTLE_ENGAGEMENT, TC_UNDECLARED_ATTACK_WITHDRAW, TC_FLEE_INVASION, TC_INSTIGATE_SIEGE
-- ---@TODO : make some codetoreport all unknown transgression type strings, and also do that for everything similarto TRANSGRESSION 
-- ---@param eventData eventTrigger
 --function onTransgression(eventData)
    --log.always('Transgression : '..eventData.resourceDescription..' ('..eventData.faction.name..' => '..eventData.targetFaction.name..')')
--	internationalAffairs.siegePenalty(eventData)
 --end



-- ---Exports: faction, targetFaction, religion, targetReligion
-- ---@param eventData eventTrigger
-- function onFactionWarDeclared(eventData)
--     print('onFactionWarDeclared triggered')
--     -- print('War Declared : '..eventData.resourceDescription..' ('..eventData.faction.name..' => '..eventData.targetFaction.name..')')
--     log.always('War Declared : '..eventData.faction.name..' => '..eventData.targetFaction.name)
-- end


---tinkering with crusade refactor
---local function removeTraits(characterRecord, traitList)
---    for i, traitName in pairs(traitList) do
---        ---@TODO check if not bugging
---        characterRecord:removeTraits(traitName)
---        log.info('trait '..traitName..' removed for character: '..character.characterRecord.localizedDisplayName)
---    end
---end
---
----- After the crusade the players characters who participate in it receive a trait that gives them movement bonus.
------@param character character
---function module.removeCrusaderReturningTrait(character)
---    if character:getTypeID() ~= characterType.named_character then return end
---    if crusade == nil then return end
---    lastCrusadeTurn = crusade.endTurn -- better here to be remembered
---    local turnsAfterCrusade = M2TW.campaign.turnNumber - lastCrusadeTurn
---    -- first condition if long after crusade is finished then just return to main.lua immediately
---    if  turnsAfterCrusade > numTurntoRemoveTrait then return end
---    log.info("+Crusade: turns after the last Crusade/Jihad: "..tostring(turnsAfterCrusade) )
---    if  turnsAfterCrusade == numTurntoRemoveTrait or character.faction:isNeighbourRegion(character.regionID) then 
---        removeTraits(character, {'CrusaderReturning', 'JihadReturning'})
---    end
---end




local CharacterRecordLogger = {}

function CharacterRecordLogger.logAssaultData(log, eventData)
    local function logCharacterData(label, character)
        if not character then
            log.always("[CharacterRecord] " .. label .. " não encontrado")
            return
        end

        local success, fullName = pcall(function() return character.fullName end)
        fullName = success and fullName or "DESCONHECIDO"

        local successIndex, index = pcall(function() return character.index end)
        log.always("[CharacterRecord] Index de " .. fullName .. ": " .. tostring(successIndex and index or "N/A"))

        local ancNum = 0
        pcall(function() ancNum = character.ancNum or 0 end)
        log.always("[CharacterRecord] Ancillaries de " .. fullName .. " (" .. ancNum .. "):")
        for i = 0, ancNum - 1 do
            local status, anc = pcall(function() return character:getAncillary(i) end)
            if status and anc and anc.name then
                log.always(" - " .. anc.name)
            else
                log.always(" - (ancillary nula ou erro no índice " .. i .. ")")
            end
        end

        local traits = nil
        pcall(function() traits = character:getTraits() end)
        if not traits then
            log.always("[CharacterRecord] Nenhum trait encontrado para " .. fullName)
        else
            log.always("[CharacterRecord] Traits de " .. fullName .. ":")
            local index = 0
            while traits do
                local traitName = tostring(traits.name or "NIL")
                local traitLevel = tostring(traits.level or "NIL")
                log.always(" - Trait " .. index .. " - Name: " .. traitName .. " - Level: " .. traitLevel)
                traits = traits.nextTrait
                index = index + 1
            end
        end

        local origFac = nil
        pcall(function() origFac = character.originalFaction end)
        log.always("[CharacterRecord] originalFaction de " .. fullName .. ": " .. tostring(origFac or "não disponível"))

        local kills = nil
        pcall(function() kills = character.kills end)
        log.always("[CharacterRecord] Kills de " .. fullName .. ": " .. tostring(kills or "não disponível"))

        if character.character and character.character.xCoord ~= nil and character.character.yCoord ~= nil then
            log.always("[CharacterRecord] Coordenadas de " .. fullName .. ": " .. tostring(character.character.xCoord) .. ", " .. tostring(character.character.yCoord))
        else
            log.always("[CharacterRecord] Coordenadas de " .. fullName .. " não disponíveis")
        end

        -- Adicionando registro das unidades do exército do personagem
        local army = nil
        local armySuccess = pcall(function() army = character.character and character.character.army end)
        if not armySuccess or not army then
            log.always("[CharacterRecord] Nenhum exército associado a " .. fullName .. " (ou erro ao acessar character.army)")
            return
        end

        -- Obter a facção do personagem para garantir contexto
        local faction = nil
        local factionSuccess = pcall(function() faction = character.faction end)
        if not factionSuccess or not faction then
            log.always("[CharacterRecord] Facção não encontrada para " .. fullName)
            return
        end
        log.always("[CharacterRecord] Facção de " .. fullName .. ": " .. (faction.name or "DESCONHECIDA"))

        local unitCount = 0
        local unitCountSuccess = pcall(function() unitCount = army.numOfUnits or army.numUnits or 0 end)
        if not unitCountSuccess then
            log.always("[CharacterRecord] Erro ao acessar numUnits/numOfUnits para " .. fullName)
            return
        end

        log.always("[CharacterRecord] unitCount inicial para " .. fullName .. ": " .. tostring(unitCount))
        if unitCount == 0 then
            log.always("[CharacterRecord] Exército de " .. fullName .. " reporta 0 unidades")
            -- Tenta acessar as unidades diretamente como última verificação
            local testUnit = nil
            local testSuccess = pcall(function() testUnit = army:getUnit(0) end)
            if testSuccess and testUnit then
                log.always("[CharacterRecord] Unidade 0 encontrada, tentando listar unidades manualmente para " .. fullName)
                local i = 0
                while true do
                    local status, unit = pcall(function() return army:getUnit(i) end)
                    if not status or not unit then
                        if i == 0 then
                            log.always("[CharacterRecord] Nenhuma unidade encontrada ao iterar manualmente para " .. fullName)
                        end
                        break
                    end
                    local unitType = "DESCONHECIDO"
                    local soldierCount = "N/A"
                    local experience = "N/A"
                    local armor = "N/A"
                    local weapon = "N/A"
                    pcall(function() unitType = unit.eduEntry and unit.eduEntry.type or "DESCONHECIDO" end)
                    pcall(function() soldierCount = tostring(unit.soldiers or "N/A") end)
                    pcall(function() experience = tostring(unit.exp or "N/A") end)
                    pcall(function() armor = tostring(unit.armor or "N/A") end)
                    pcall(function() weapon = tostring(unit.weapon or "N/A") end)
                    log.always(" - Unidade " .. i .. ": Type: " .. unitType .. ", Soldados: " .. soldierCount .. ", Experiência: " .. experience .. ", Armadura: " .. armor .. ", Arma: " .. weapon)
                    i = i + 1
                end
            else
                log.always("[CharacterRecord] Nenhuma unidade encontrada ao tentar acessar diretamente para " .. fullName)
            end
        else
            log.always("[CharacterRecord] Unidades no exército de " .. fullName .. " (" .. unitCount .. "):")
            for i = 0, unitCount - 1 do
                local status, unit = pcall(function() return army:getUnit(i) end)
                if status and unit then
                    local unitType = "DESCONHECIDO"
                    local soldierCount = "N/A"
                    local experience = "N/A"
                    local armor = "N/A"
                    local weapon = "N/A"
                    pcall(function() unitType = unit.eduEntry and unit.eduEntry.type or "DESCONHECIDO" end)
                    pcall(function() soldierCount = tostring(unit.soldiers or "N/A") end)
                    pcall(function() experience = tostring(unit.exp or "N/A") end)
                    pcall(function() armor = tostring(unit.armor or "N/A") end)
                    pcall(function() weapon = tostring(unit.weapon or "N/A") end)
                    log.always(" - Unidade " .. i .. ": Type: " .. unitType .. ", Soldados: " .. soldierCount .. ", Experiência: " .. experience .. ", Armadura: " .. armor .. ", Arma: " .. weapon)
                else
                    log.always(" - (unidade nula ou erro no índice " .. i .. ")")
                end
            end
        end
    end

    log.always("[CharacterRecord] Evento onGeneralAssaultsGeneral disparado")
    pcall(function() logCharacterData("General 1", eventData.character) end)
    pcall(function() logCharacterData("General 2", eventData.targetCharacter) end)
end

return CharacterRecordLogger